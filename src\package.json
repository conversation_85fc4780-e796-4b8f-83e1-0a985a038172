{"name": "roo-code-134", "displayName": "%extension.displayName%", "description": "%extension.description%", "publisher": "RooVeterinaryInc", "version": "3.19.5", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0", "node": "20.19.2"}, "author": {"name": "Roo Code 134"}, "repository": {"type": "git", "url": "https://github.com/RooCodeInc/Roo-Code-134"}, "homepage": "https://github.com/RooCodeInc/Roo-Code-134", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama", "roo code 134", "roocode134"], "activationEvents": ["onLanguage", "onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "roo-code-134-ActivityBar", "title": "%views.activitybar.title%", "icon": "assets/icons/icon.svg"}]}, "views": {"roo-code-134-ActivityBar": [{"type": "webview", "id": "roo-code-134.<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "%views.sidebar.name%"}]}, "commands": [{"command": "roo-code-134.plusButtonClicked", "title": "%command.newTask.title%", "icon": "$(add)"}, {"command": "roo-code-134.m<PERSON><PERSON><PERSON><PERSON><PERSON>Clicked", "title": "%command.mcpServers.title%", "icon": "$(server)"}, {"command": "roo-code-134.prompts<PERSON><PERSON>onClicked", "title": "%command.prompts.title%", "icon": "$(organization)"}, {"command": "roo-code-134.historyButtonClicked", "title": "%command.history.title%", "icon": "$(history)"}, {"command": "roo-code-134.popoutButtonClicked", "title": "%command.openInEditor.title%", "icon": "$(link-external)"}, {"command": "roo-code-134.accountButtonClicked", "title": "Account", "icon": "$(account)", "when": "config.roo-code-134.rooCodeCloudEnabled"}, {"command": "roo-code-134.<PERSON><PERSON><PERSON>onClicked", "title": "%command.settings.title%", "icon": "$(settings-gear)"}, {"command": "roo-code-134.openInNewTab", "title": "%command.openInNewTab.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.explainCode", "title": "%command.explainCode.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.fixCode", "title": "%command.fixCode.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.improveCode", "title": "%command.improveCode.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.addToContext", "title": "%command.addToContext.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.newTask", "title": "%command.newTask.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.terminalAddToContext", "title": "%command.terminal.addToContext.title%", "category": "Terminal"}, {"command": "roo-code-134.terminalFixCommand", "title": "%command.terminal.fixCommand.title%", "category": "Terminal"}, {"command": "roo-code-134.terminalExplainCommand", "title": "%command.terminal.explainCommand.title%", "category": "Terminal"}, {"command": "roo-code-134.setCustomStoragePath", "title": "%command.setCustomStoragePath.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.focusInput", "title": "%command.focusInput.title%", "category": "%configuration.title%"}, {"command": "roo-code-134.acceptInput", "title": "%command.acceptInput.title%", "category": "%configuration.title%"}], "menus": {"editor/context": [{"submenu": "roo-code-134.contextMenu", "group": "navigation"}], "roo-code-134.contextMenu": [{"command": "roo-code-134.addToContext", "group": "1_actions@1"}, {"command": "roo-code-134.explainCode", "group": "1_actions@2"}, {"command": "roo-code-134.improveCode", "group": "1_actions@3"}], "terminal/context": [{"submenu": "roo-code-134.terminalMenu", "group": "navigation"}], "roo-code-134.terminalMenu": [{"command": "roo-code-134.terminalAddToContext", "group": "1_actions@1"}, {"command": "roo-code-134.terminalFixCommand", "group": "1_actions@2"}, {"command": "roo-code-134.terminalExplainCommand", "group": "1_actions@3"}], "view/title": [{"command": "roo-code-134.plusButtonClicked", "group": "navigation@1", "when": "view == roo-code-134.SidebarProvider"}, {"command": "roo-code-134.prompts<PERSON><PERSON>onClicked", "group": "navigation@2", "when": "view == roo-code-134.SidebarProvider"}, {"command": "roo-code-134.m<PERSON><PERSON><PERSON><PERSON><PERSON>Clicked", "group": "navigation@3", "when": "view == roo-code-134.SidebarProvider"}, {"command": "roo-code-134.historyButtonClicked", "group": "navigation@4", "when": "view == roo-code-134.SidebarProvider"}, {"command": "roo-code-134.popoutButtonClicked", "group": "navigation@5", "when": "view == roo-code-134.SidebarProvider"}, {"command": "roo-code-134.accountButtonClicked", "group": "navigation@6", "when": "view == roo-code-134.SidebarProvider && config.roo-code-134.rooCodeCloudEnabled"}, {"command": "roo-code-134.<PERSON><PERSON><PERSON>onClicked", "group": "navigation@7", "when": "view == roo-code-134.SidebarProvider"}], "editor/title": [{"command": "roo-code-134.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}, {"command": "roo-code-134.prompts<PERSON><PERSON>onClicked", "group": "navigation@2", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}, {"command": "roo-code-134.m<PERSON><PERSON><PERSON><PERSON><PERSON>Clicked", "group": "navigation@3", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}, {"command": "roo-code-134.historyButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}, {"command": "roo-code-134.popoutButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}, {"command": "roo-code-134.accountButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider && config.roo-code-134.rooCodeCloudEnabled"}, {"command": "roo-code-134.<PERSON><PERSON><PERSON>onClicked", "group": "navigation@7", "when": "activeWebviewPanelId == roo-code-134.TabPanelProvider"}]}, "submenus": [{"id": "roo-code-134.contextMenu", "label": "%views.contextMenu.label%"}, {"id": "roo-code-134.terminalMenu", "label": "%views.terminalMenu.label%"}], "configuration": {"title": "%configuration.title%", "properties": {"roo-code-134.allowedCommands": {"type": "array", "items": {"type": "string"}, "default": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "description": "%commands.allowedCommands.description%"}, "roo-code-134.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "%settings.vsCodeLmModelSelector.vendor.description%"}, "family": {"type": "string", "description": "%settings.vsCodeLmModelSelector.family.description%"}}, "description": "%settings.vsCodeLmModelSelector.description%"}, "roo-code-134.customStoragePath": {"type": "string", "default": "", "description": "%settings.customStoragePath.description%"}, "roo-code-134.rooCodeCloudEnabled": {"type": "boolean", "default": false, "description": "%settings.rooCodeCloudEnabled.description%"}}}}, "scripts": {"lint": "eslint . --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "pretest": "turbo run bundle --cwd ..", "test": "jest -w=40% && vitest run", "format": "prettier --write .", "bundle": "node esbuild.mjs", "vscode:prepublish": "pnpm bundle --production", "vsix": "mkdirp ../bin && npx vsce package --no-dependencies --out ../bin", "publish:marketplace": "vsce publish --no-dependencies && ovsx publish --no-dependencies", "watch:bundle": "pnpm bundle --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "clean": "rimraf README.md CHANGELOG.md LICENSE dist mock .turbo"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.37.0", "@anthropic-ai/vertex-sdk": "^0.7.0", "@aws-sdk/client-bedrock-runtime": "^3.779.0", "@aws-sdk/credential-providers": "^3.806.0", "@google/genai": "^0.13.0", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.9.0", "@roo-code-134/cloud": "workspace:^", "@roo-code-134/ipc": "workspace:^", "@roo-code-134/telemetry": "workspace:^", "@roo-code-134/types": "workspace:^", "@qdrant/js-client-rest": "^1.14.0", "@types/lodash.debounce": "^4.0.9", "@vscode/codicons": "^0.0.36", "async-mutex": "^0.5.0", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "diff-match-patch": "^1.0.5", "fast-deep-equal": "^3.1.3", "fast-xml-parser": "^4.5.1", "fastest-levenshtein": "^1.0.16", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "google-auth-library": "^9.15.1", "i18next": "^24.2.2", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "langfuse": "^3.30.0", "lodash.debounce": "^4.0.8", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "node-cache": "^5.1.2", "node-ipc": "^12.0.0", "openai": "^4.78.1", "os-name": "^6.0.0", "p-limit": "^6.2.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "pkce-challenge": "^4.1.0", "pretty-bytes": "^6.1.1", "ps-tree": "^1.2.0", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "reconnecting-eventsource": "^1.6.4", "sanitize-filename": "^1.6.3", "say": "^0.16.0", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "sound-play": "^1.1.0", "string-similarity": "^4.0.4", "strip-ansi": "^7.1.0", "strip-bom": "^5.0.0", "tiktoken": "^1.0.21", "tmp": "^0.2.3", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "uuid": "^11.1.0", "vscode-material-icons": "^0.1.1", "web-tree-sitter": "^0.22.6", "workerpool": "^9.2.0", "yaml": "^2.8.0", "zod": "^3.24.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@roo-code-134/build": "workspace:^", "@roo-code-134/config-eslint": "workspace:^", "@roo-code-134/config-typescript": "workspace:^", "@types/clone-deep": "^4.0.4", "@types/debug": "^4.1.12", "@types/diff": "^5.2.1", "@types/diff-match-patch": "^1.0.36", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@types/node-cache": "^4.1.3", "@types/node-ipc": "^9.2.3", "@types/ps-tree": "^1.1.6", "@types/string-similarity": "^4.0.2", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/vscode": "^1.84.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "3.3.2", "esbuild": "^0.25.0", "execa": "^9.5.2", "glob": "^11.0.1", "jest": "^29.7.0", "jest-simple-dot-reporter": "^1.0.5", "mkdirp": "^3.0.1", "nock": "^14.0.4", "npm-run-all2": "^8.0.1", "ovsx": "0.10.4", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "5.8.3", "vitest": "^3.1.3", "zod-to-ts": "^1.2.0"}}