exports.id=994,exports.ids=[994],exports.modules={176:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Toaster:()=>a});var r=o(3641),n=o(64925),s=o(91149);let a=({...e})=>{let{theme:t="system"}=(0,n.D)();return(0,r.jsx)(s.l$,{theme:t,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...e})}},1196:(e,t,o)=>{"use strict";o.r(t),o.d(t,{ScrollArea:()=>a,ScrollBar:()=>i});var r=o(3641);o(44508);var n=o(1036),s=o(10244);function a({className:e,children:t,viewportRef:o,...a}){return(0,r.jsxs)(n.bL,{"data-slot":"scroll-area",className:(0,s.cn)("relative",e),...a,children:[(0,r.jsx)(n.LM,{ref:o,"data-slot":"scroll-area-viewport",className:"ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] focus-visible:ring-4 focus-visible:outline-1",children:t}),(0,r.jsx)(i,{}),(0,r.jsx)(n.OK,{})]})}function i({className:e,orientation:t="vertical",...o}){return(0,r.jsx)(n.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,s.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...o,children:(0,r.jsx)(n.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},2809:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Table:()=>n,TableBody:()=>a,TableCaption:()=>p,TableCell:()=>d,TableFooter:()=>i,TableHead:()=>l,TableHeader:()=>s,TableRow:()=>c});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","Table"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableHeader"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableBody"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableFooter"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableHead"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableRow"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableCell"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\table.tsx","TableCaption")},8442:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Tooltip:()=>i,TooltipContent:()=>c,TooltipProvider:()=>a,TooltipTrigger:()=>l});var r=o(3641);o(44508);var n=o(83993),s=o(10244);function a({delayDuration:e=0,...t}){return(0,r.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,r.jsx)(a,{children:(0,r.jsx)(n.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,r.jsx)(n.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:o,...a}){return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-sm px-3 py-1.5 text-xs text-balance",e),...a,children:[o,(0,r.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},8817:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Tabs:()=>n,TabsContent:()=>s,TabsList:()=>a,TabsTrigger:()=>i});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Tabs() from the server but Tabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tabs.tsx","Tabs"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsContent() from the server but TabsContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tabs.tsx","TabsContent"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsList() from the server but TabsList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tabs.tsx","TabsList"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TabsTrigger() from the server but TabsTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tabs.tsx","TabsTrigger")},10244:(e,t,o)=>{"use strict";o.d(t,{cn:()=>s});var r=o(64955),n=o(23434);function s(...e){return(0,n.QP)((0,r.$)(e))}},11987:(e,t,o)=>{"use strict";o.d(t,{ReactQueryProvider:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call ReactQueryProvider() from the server but ReactQueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\providers\\react-query-provider.tsx","ReactQueryProvider")},16435:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Label:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\label.tsx","Label")},16667:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Tabs:()=>i,TabsContent:()=>d,TabsList:()=>l,TabsTrigger:()=>c});var r=o(3641),n=o(44508),s=o(34600),a=o(10244);let i=s.bL,l=n.forwardRef(({className:e,...t},o)=>{let[i,l]=(0,n.useState)({left:0,top:0,width:0,height:0}),c=(0,n.useRef)(null),d=n.useCallback(()=>{if(!c.current)return;let e=c.current.querySelector('[data-state="active"]');if(!e)return;let t=e.getBoundingClientRect(),o=c.current.getBoundingClientRect();requestAnimationFrame(()=>{l({left:t.left-o.left,top:t.top-o.top,width:t.width,height:t.height})})},[]);return(0,n.useEffect)(()=>{let e=setTimeout(d,0);window.addEventListener("resize",d);let t=new MutationObserver(d);return c.current&&t.observe(c.current,{attributes:!0,childList:!0,subtree:!0}),()=>{clearTimeout(e),window.removeEventListener("resize",d),t.disconnect()}},[d]),(0,r.jsxs)("div",{className:"relative",ref:c,children:[(0,r.jsx)(s.B8,{ref:o,className:(0,a.cn)("relative inline-flex items-center justify-center rounded-sm bg-primary p-0.5 text-muted-foreground",e),...t}),(0,r.jsx)("div",{className:(0,a.cn)("absolute rounded-sm transition-all duration-300 ease-in-out pointer-events-none","bg-accent/5"),style:i})]})});l.displayName=s.B8.displayName;let c=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(s.l9,{ref:o,className:(0,a.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 z-10","data-[state=active]:text-accent data-[state=active]:font-medium cursor-pointer",e),...t}));c.displayName=s.l9.displayName;let d=n.forwardRef(({className:e,...t},o)=>(0,r.jsx)(s.UC,{ref:o,className:(0,a.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));d.displayName=s.UC.displayName},20164:(e,t,o)=>{"use strict";o.r(t),o.d(t,{AlertDialog:()=>i,AlertDialogAction:()=>h,AlertDialogCancel:()=>b,AlertDialogContent:()=>p,AlertDialogDescription:()=>v,AlertDialogFooter:()=>u,AlertDialogHeader:()=>m,AlertDialogOverlay:()=>d,AlertDialogPortal:()=>c,AlertDialogTitle:()=>f,AlertDialogTrigger:()=>l});var r=o(3641);o(44508);var n=o(91916),s=o(10244),a=o(99115);function i({...e}){return(0,r.jsx)(n.bL,{"data-slot":"alert-dialog",...e})}function l({...e}){return(0,r.jsx)(n.l9,{"data-slot":"alert-dialog-trigger",...e})}function c({...e}){return(0,r.jsx)(n.ZL,{"data-slot":"alert-dialog-portal",...e})}function d({className:e,...t}){return(0,r.jsx)(n.hJ,{"data-slot":"alert-dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function p({className:e,...t}){return(0,r.jsxs)(c,{children:[(0,r.jsx)(d,{}),(0,r.jsx)(n.UC,{"data-slot":"alert-dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,s.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,r.jsx)(n.hE,{"data-slot":"alert-dialog-title",className:(0,s.cn)("text-lg font-semibold",e),...t})}function v({className:e,...t}){return(0,r.jsx)(n.VY,{"data-slot":"alert-dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function h({className:e,...t}){return(0,r.jsx)(n.rc,{className:(0,s.cn)((0,a.r)(),e),...t})}function b({className:e,...t}){return(0,r.jsx)(n.ZD,{className:(0,s.cn)((0,a.r)({variant:"outline"}),e),...t})}},21212:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Tooltip:()=>n,TooltipContent:()=>a,TooltipProvider:()=>i,TooltipTrigger:()=>s});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tooltip.tsx","Tooltip"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tooltip.tsx","TooltipTrigger"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tooltip.tsx","TooltipContent"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\tooltip.tsx","TooltipProvider")},24478:(e,t,o)=>{"use strict";o.d(t,{ThemeProvider:()=>s});var r=o(3641);o(44508);let n=(0,o(36650).default)(async()=>{},{loadableGenerated:{modules:["components\\providers\\theme-provider.tsx -> next-themes"]},ssr:!1});function s({children:e,...t}){return(0,r.jsx)(n,{...t,children:e})}},25655:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Form:()=>s,FormControl:()=>l,FormDescription:()=>c,FormField:()=>p,FormItem:()=>a,FormLabel:()=>i,FormMessage:()=>d,useFormField:()=>n});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call useFormField() from the server but useFormField is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","useFormField"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call Form() from the server but Form is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","Form"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormItem() from the server but FormItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormItem"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormLabel() from the server but FormLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormLabel"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormControl() from the server but FormControl is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormControl"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormDescription() from the server but FormDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormDescription"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormMessage() from the server but FormMessage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormMessage"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call FormField() from the server but FormField is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\form.tsx","FormField")},26160:(e,t,o)=>{"use strict";o.r(t),o.d(t,{DropdownMenu:()=>n,DropdownMenuCheckboxItem:()=>p,DropdownMenuContent:()=>i,DropdownMenuGroup:()=>l,DropdownMenuItem:()=>d,DropdownMenuLabel:()=>c,DropdownMenuPortal:()=>s,DropdownMenuRadioGroup:()=>m,DropdownMenuRadioItem:()=>u,DropdownMenuSeparator:()=>f,DropdownMenuShortcut:()=>v,DropdownMenuTrigger:()=>a});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenu() from the server but DropdownMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenu"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuPortal() from the server but DropdownMenuPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuPortal"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuTrigger() from the server but DropdownMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuTrigger"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuContent() from the server but DropdownMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuContent"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuGroup() from the server but DropdownMenuGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuGroup"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuLabel() from the server but DropdownMenuLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuLabel"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuItem() from the server but DropdownMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuItem"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuCheckboxItem() from the server but DropdownMenuCheckboxItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuCheckboxItem"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuRadioGroup() from the server but DropdownMenuRadioGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuRadioGroup"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuRadioItem() from the server but DropdownMenuRadioItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuRadioItem"),f=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuSeparator() from the server but DropdownMenuSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuSeparator"),v=(0,r.registerClientReference)(function(){throw Error("Attempted to call DropdownMenuShortcut() from the server but DropdownMenuShortcut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dropdown-menu.tsx","DropdownMenuShortcut")},35940:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Popover:()=>a,PopoverAnchor:()=>c,PopoverContent:()=>l,PopoverTrigger:()=>i});var r=o(3641);o(44508);var n=o(36751),s=o(10244);function a({...e}){return(0,r.jsx)(n.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,r.jsx)(n.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:o=4,...a}){return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"popover-content",align:t,sideOffset:o,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-sm border p-4 shadow-md outline-hidden",e),...a})})}function c({...e}){return(0,r.jsx)(n.Mz,{"data-slot":"popover-anchor",...e})}},37194:(e,t,o)=>{"use strict";o.r(t),o.d(t,{ScrollArea:()=>n,ScrollBar:()=>s});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\scroll-area.tsx","ScrollArea"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\scroll-area.tsx","ScrollBar")},37237:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,20502,23)),Promise.resolve().then(o.t.bind(o,15552,23)),Promise.resolve().then(o.t.bind(o,52052,23)),Promise.resolve().then(o.t.bind(o,807,23)),Promise.resolve().then(o.t.bind(o,42159,23)),Promise.resolve().then(o.t.bind(o,99335,23)),Promise.resolve().then(o.t.bind(o,52951,23)),Promise.resolve().then(o.t.bind(o,84417,23))},39012:(e,t,o)=>{"use strict";o.d(t,{ThemeProvider:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\providers\\theme-provider.tsx","ThemeProvider")},41582:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Popover:()=>n,PopoverAnchor:()=>i,PopoverContent:()=>a,PopoverTrigger:()=>s});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\popover.tsx","Popover"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\popover.tsx","PopoverTrigger"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\popover.tsx","PopoverContent"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call PopoverAnchor() from the server but PopoverAnchor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\popover.tsx","PopoverAnchor")},46016:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Command:()=>l,CommandDialog:()=>c,CommandEmpty:()=>m,CommandGroup:()=>u,CommandInput:()=>d,CommandItem:()=>v,CommandList:()=>p,CommandSeparator:()=>f,CommandShortcut:()=>h});var r=o(3641);o(44508);var n=o(15610),s=o(8839),a=o(10244),i=o(49431);function l({className:e,...t}){return(0,r.jsx)(n.uB,{"data-slot":"command",className:(0,a.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-sm",e),...t})}function c({title:e="Command Palette",description:t="Search for a command to run...",children:o,...n}){return(0,r.jsxs)(i.Dialog,{...n,children:[(0,r.jsxs)(i.DialogHeader,{className:"sr-only",children:[(0,r.jsx)(i.DialogTitle,{children:e}),(0,r.jsx)(i.DialogDescription,{children:t})]}),(0,r.jsx)(i.DialogContent,{className:"overflow-hidden p-0",children:(0,r.jsx)(l,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:o})})]})}function d({className:e,...t}){return(0,r.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,r.jsx)(s.A,{className:"size-4 shrink-0 opacity-50"}),(0,r.jsx)(n.uB.Input,{"data-slot":"command-input",className:(0,a.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-sm bg-transparent py-3 outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...t})]})}function p({className:e,...t}){return(0,r.jsx)(n.uB.List,{"data-slot":"command-list",className:(0,a.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...t})}function m({...e}){return(0,r.jsx)(n.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center",...e})}function u({className:e,...t}){return(0,r.jsx)(n.uB.Group,{"data-slot":"command-group",className:(0,a.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t})}function f({className:e,...t}){return(0,r.jsx)(n.uB.Separator,{"data-slot":"command-separator",className:(0,a.cn)("bg-accent/5 -mx-1 h-px",e),...t})}function v({className:e,...t}){return(0,r.jsx)(n.uB.Item,{"data-slot":"command-item",className:(0,a.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-xs px-2 py-1.5 outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","text-foreground active:opacity-80 cursor-pointer group",e),...t})}function h({className:e,...t}){return(0,r.jsx)("span",{"data-slot":"command-shortcut",className:(0,a.cn)("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}},46390:(e,t,o)=>{"use strict";o.r(t),o.d(t,{AlertDialog:()=>n,AlertDialogAction:()=>u,AlertDialogCancel:()=>f,AlertDialogContent:()=>l,AlertDialogDescription:()=>m,AlertDialogFooter:()=>d,AlertDialogHeader:()=>c,AlertDialogOverlay:()=>a,AlertDialogPortal:()=>s,AlertDialogTitle:()=>p,AlertDialogTrigger:()=>i});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialog() from the server but AlertDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialog"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogPortal() from the server but AlertDialogPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogPortal"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogOverlay() from the server but AlertDialogOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogOverlay"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogTrigger() from the server but AlertDialogTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogTrigger"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogContent() from the server but AlertDialogContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogContent"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogHeader() from the server but AlertDialogHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogHeader"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogFooter() from the server but AlertDialogFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogFooter"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogTitle() from the server but AlertDialogTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogTitle"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogDescription() from the server but AlertDialogDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogDescription"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogAction() from the server but AlertDialogAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogAction"),f=(0,r.registerClientReference)(function(){throw Error("Attempted to call AlertDialogCancel() from the server but AlertDialogCancel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\alert-dialog.tsx","AlertDialogCancel")},46886:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Drawer:()=>n,DrawerClose:()=>l,DrawerContent:()=>c,DrawerDescription:()=>u,DrawerFooter:()=>p,DrawerHeader:()=>d,DrawerOverlay:()=>a,DrawerPortal:()=>s,DrawerTitle:()=>m,DrawerTrigger:()=>i});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Drawer() from the server but Drawer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","Drawer"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerPortal() from the server but DrawerPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerPortal"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerOverlay() from the server but DrawerOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerOverlay"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerTrigger() from the server but DrawerTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerTrigger"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerClose() from the server but DrawerClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerClose"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerContent() from the server but DrawerContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerContent"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerHeader() from the server but DrawerHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerHeader"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerFooter() from the server but DrawerFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerFooter"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerTitle() from the server but DrawerTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerTitle"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call DrawerDescription() from the server but DrawerDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\drawer.tsx","DrawerDescription")},49431:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Dialog:()=>i,DialogClose:()=>d,DialogContent:()=>m,DialogDescription:()=>h,DialogFooter:()=>f,DialogHeader:()=>u,DialogOverlay:()=>p,DialogPortal:()=>c,DialogTitle:()=>v,DialogTrigger:()=>l});var r=o(3641);o(44508);var n=o(36554),s=o(52819),a=o(10244);function i({...e}){return(0,r.jsx)(n.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,r.jsx)(n.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,r.jsx)(n.ZL,{"data-slot":"dialog-portal",...e})}function d({...e}){return(0,r.jsx)(n.bm,{"data-slot":"dialog-close",...e})}function p({className:e,...t}){return(0,r.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,a.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function m({className:e,children:t,...o}){return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(p,{}),(0,r.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,a.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[t,(0,r.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(s.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,a.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function f({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,a.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function v({className:e,...t}){return(0,r.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,a.cn)("text-lg leading-none font-semibold",e),...t})}function h({className:e,...t}){return(0,r.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}},50266:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Toaster:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\sonner.tsx","Toaster")},50805:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,38360,23)),Promise.resolve().then(o.t.bind(o,83510,23)),Promise.resolve().then(o.t.bind(o,61430,23)),Promise.resolve().then(o.t.bind(o,87225,23)),Promise.resolve().then(o.t.bind(o,80989,23)),Promise.resolve().then(o.t.bind(o,17445,23)),Promise.resolve().then(o.t.bind(o,36557,23)),Promise.resolve().then(o.t.bind(o,55895,23))},54730:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Slider:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call Slider() from the server but Slider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\slider.tsx","Slider")},54927:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Select:()=>c,SelectContent:()=>u,SelectGroup:()=>d,SelectItem:()=>v,SelectLabel:()=>f,SelectScrollDownButton:()=>g,SelectScrollUpButton:()=>b,SelectSeparator:()=>h,SelectTrigger:()=>m,SelectValue:()=>p});var r=o(3641);o(44508);var n=o(78555),s=o(83061),a=o(48411),i=o(31796),l=o(10244);function c({...e}){return(0,r.jsx)(n.bL,{"data-slot":"select",...e})}function d({...e}){return(0,r.jsx)(n.YJ,{"data-slot":"select-group",...e})}function p({...e}){return(0,r.jsx)(n.WT,{"data-slot":"select-value",...e})}function m({className:e,size:t="default",children:o,...a}){return(0,r.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex w-fit items-center justify-between gap-2 rounded-sm px-3 py-2 whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","border border-input bg-input hover:opacity-80 cursor-pointer",e),...a,children:[o,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function u({className:e,children:t,position:o="popper",...s}){return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-sm shadow-md","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:o,...s,children:[(0,r.jsx)(b,{}),(0,r.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(g,{})]})})}function f({className:e,...t}){return(0,r.jsx)(n.JU,{"data-slot":"select-label",className:(0,l.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...t})}function v({className:e,children:t,...o}){return(0,r.jsxs)(n.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-xs py-1.5 pr-8 pl-2 outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","text-foreground active:opacity-80 cursor-pointer group",e),...o,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(a.A,{className:"text-accent group-focus:text-accent-foreground size-4"})})}),(0,r.jsx)(n.p4,{children:t})]})}function h({className:e,...t}){return(0,r.jsx)(n.wv,{"data-slot":"select-separator",className:(0,l.cn)("bg-border pointer-events-none -mx-1 my-1 h-px",e),...t})}function b({className:e,...t}){return(0,r.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}function g({className:e,...t}){return(0,r.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(s.A,{className:"size-4"})})}},56910:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>I,metadata:()=>T});var r=o(90811),n=o(58788),s=o.n(n),a=o(60482),i=o.n(a),l=o(11987),c=o(39012);o(46390);var d=o(4446),p=o(97664),m=o(26199),u=o(71537),f=o(82612);function v(...e){return(0,f.QP)((0,u.$)(e))}let h=(0,m.F)("inline-flex items-center justify-center rounded-sm border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function b({className:e,variant:t,asChild:o=!1,...n}){let s=o?p.Slot:"span";return(0,r.jsx)(s,{"data-slot":"badge",className:v(h({variant:t}),e),...n})}(0,m.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:opacity-80 active:scale-95 cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs [&_svg]:text-accent",destructive:"bg-destructive text-white shadow-xs focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input",secondary:"bg-secondary text-secondary-foreground shadow-xs [&_svg]:text-ring",ghost:"hover:bg-primary hover:text-primary-foreground",link:"text-accent underline-offset-4 hover:underline h-4! px-1! rounded-none",input:"bg-input text-input-foreground active:scale-100 shadow-xs"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5 text-sm",lg:"h-10 px-6 has-[>svg]:px-4 text-lg",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});var g=o(64582);o(84533),o(46886),o(26160),o(25655),o(16435);var C=o(40453),x=o.n(C),w=o(61105),D=o(41128),y=o(39453),R=o(41582);let P=(0,m.F)("px-2 py-1",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"bg-background"}},defaultVariants:{variant:"default"}});d.forwardRef(({options:e,onValueChange:t,variant:o,defaultValue:n=[],placeholder:s="Select options",maxCount:a=3,modalPopover:i=!1,className:l,...c},p)=>{let[m,u]=d.useState(n),[f,h]=d.useState(!1),C=e=>{let o=m.includes(e)?m.filter(t=>t!==e):[...m,e];u(o),t(o)},j=()=>{let e=m.slice(0,a);u(e),t(e)},k=d.useRef(new Map),A=d.useRef(""),T=d.useCallback((t,o)=>{if(A.current!==o)for(let{obj:{value:t},score:r}of(A.current=o,k.current.clear(),x().go(o,e,{key:"label"})))k.current.set(t,r);return"all"===t?.01*(k.current.size>1):k.current.get(t)??0},[e]);return(0,r.jsxs)(R.Popover,{open:f,onOpenChange:h,modal:i,children:[(0,r.jsx)(R.PopoverTrigger,{asChild:!0,children:(0,r.jsx)("div",{ref:p,...c,onClick:()=>{h(e=>!e)},className:v("flex w-full rounded-sm min-h-9 h-auto items-center justify-between [&_svg]:pointer-events-auto","font-medium border border-input bg-input hover:opacity-80 cursor-pointer",l),children:m.length>0?(0,r.jsx)("div",{className:"flex justify-between items-center w-full",children:(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-1 p-1",children:[m.slice(0,a).map(t=>(0,r.jsx)(b,{className:v(P({variant:o})),children:(0,r.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,r.jsx)("div",{children:e.find(e=>e.value===t)?.label}),(0,r.jsx)("div",{onClick:e=>{e.stopPropagation(),C(t)},className:"cursor-pointer",children:(0,r.jsx)(w.A,{className:"size-4 rounded-full p-0.5 bg-accent/5"})})]})},t)),m.length>a&&(0,r.jsx)(b,{className:v("text-ring",P({variant:o})),children:(0,r.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,r.jsx)("div",{children:`+ ${m.length-a} more`}),(0,r.jsx)("div",{onClick:e=>{e.stopPropagation(),j()},className:"cursor-pointer",children:(0,r.jsx)(w.A,{className:"size-4 rounded-full p-0.5 bg-ring/5"})})]})})]})}):(0,r.jsxs)("div",{className:"flex items-center justify-between w-full mx-auto",children:[(0,r.jsx)("span",{className:"text-muted-foreground mx-3",children:s}),(0,r.jsx)(D.A,{className:"opacity-50 size-4 mx-2"})]})})}),(0,r.jsx)(R.PopoverContent,{className:"p-0 w-[var(--radix-popover-trigger-width)]",align:"start",onEscapeKeyDown:()=>h(!1),children:(0,r.jsxs)(g.Command,{filter:T,children:[(0,r.jsx)(g.CommandInput,{placeholder:"Search",onKeyDown:e=>{if("Enter"===e.key)h(!0);else if("Backspace"===e.key&&!e.currentTarget.value){let e=[...m];e.pop(),u(e),t(e)}}}),(0,r.jsxs)(g.CommandList,{children:[(0,r.jsx)(g.CommandEmpty,{children:"No results found."}),(0,r.jsxs)(g.CommandGroup,{children:[e.map(e=>(0,r.jsxs)(g.CommandItem,{value:e.value,onSelect:()=>C(e.value),className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:e.label}),(0,r.jsx)(y.A,{className:v("text-accent group-data-[selected=true]:text-accent-foreground size-4",{"opacity-0":!m.includes(e.value)})})]},e.value)),(0,r.jsx)(g.CommandItem,{value:"all",onSelect:()=>{let e=Array.from(k.current.keys());if(m.length===e.length&&m.sort().join(",")===e.sort().join(",")){u([]),t([]);return}u(e),t(e)},className:"flex items-center justify-between",children:(0,r.jsx)("span",{children:"Select All"})},"all")]})]})]})})]})}).displayName="MultiSelect",o(37194),o(82573),o(85304),o(54730);var j=o(50266);o(2809),o(8817),o(21212);var k=o(72024);let A=()=>(0,r.jsx)("div",{className:"flex items-center justify-between border-b px-12 py-6",children:(0,r.jsx)(k.HoppingLogo,{})});o(88773);let T={title:"Roo Code Evals"};function I({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${s().variable} ${i().variable} font-sans antialiased pb-12`,children:[(0,r.jsx)(c.ThemeProvider,{attribute:"class",forcedTheme:"dark",disableTransitionOnChange:!0,children:(0,r.jsxs)(l.ReactQueryProvider,{children:[(0,r.jsx)(A,{}),e]})}),(0,r.jsx)(j.Toaster,{})]})})}},61805:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Form:()=>c,FormControl:()=>h,FormDescription:()=>b,FormField:()=>p,FormItem:()=>f,FormLabel:()=>v,FormMessage:()=>g,useFormField:()=>m});var r=o(3641),n=o(44508),s=o(36118),a=o(73466),i=o(10244),l=o(76021);let c=a.Op,d=n.createContext({}),p=({...e})=>(0,r.jsx)(d.Provider,{value:{name:e.name},children:(0,r.jsx)(a.xI,{...e})}),m=()=>{let e=n.useContext(d),t=n.useContext(u),{getFieldState:o}=(0,a.xW)(),r=(0,a.lN)({name:e.name}),s=o(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...s}},u=n.createContext({});function f({className:e,...t}){let o=n.useId();return(0,r.jsx)(u.Provider,{value:{id:o},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...t})})}function v({className:e,...t}){let{error:o,formItemId:n}=m();return(0,r.jsx)(l.Label,{"data-slot":"form-label","data-error":!!o,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...t})}function h({...e}){let{error:t,formItemId:o,formDescriptionId:n,formMessageId:a}=m();return(0,r.jsx)(s.Slot,{"data-slot":"form-control",id:o,"aria-describedby":t?`${n} ${a}`:`${n}`,"aria-invalid":!!t,...e})}function b({className:e,...t}){let{formDescriptionId:o}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:o,className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function g({className:e,...t}){let{error:o,formMessageId:n}=m(),s=o?String(o?.message??""):t.children;return s?(0,r.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-destructive text-sm",e),...t,children:s}):null}},62604:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Drawer:()=>a,DrawerClose:()=>c,DrawerContent:()=>p,DrawerDescription:()=>v,DrawerFooter:()=>u,DrawerHeader:()=>m,DrawerOverlay:()=>d,DrawerPortal:()=>l,DrawerTitle:()=>f,DrawerTrigger:()=>i});var r=o(3641);o(44508);var n=o(45333),s=o(10244);function a({...e}){return(0,r.jsx)(n._s.Root,{"data-slot":"drawer",...e})}function i({...e}){return(0,r.jsx)(n._s.Trigger,{"data-slot":"drawer-trigger",...e})}function l({...e}){return(0,r.jsx)(n._s.Portal,{"data-slot":"drawer-portal",...e})}function c({...e}){return(0,r.jsx)(n._s.Close,{"data-slot":"drawer-close",...e})}function d({className:e,...t}){return(0,r.jsx)(n._s.Overlay,{"data-slot":"drawer-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function p({className:e,children:t,...o}){return(0,r.jsxs)(l,{"data-slot":"drawer-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(n._s.Content,{"data-slot":"drawer-content",className:(0,s.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-sm data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-sm data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",e),...o,children:[(0,r.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),t]})]})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"drawer-header",className:(0,s.cn)("flex flex-col gap-1.5 py-4",e),...t})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"drawer-footer",className:(0,s.cn)("mt-auto flex flex-col gap-2 py-4",e),...t})}function f({className:e,...t}){return(0,r.jsx)(n._s.Title,{"data-slot":"drawer-title",className:(0,s.cn)("text-foreground font-semibold",e),...t})}function v({className:e,...t}){return(0,r.jsx)(n._s.Description,{"data-slot":"drawer-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}},64582:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Command:()=>n,CommandDialog:()=>s,CommandEmpty:()=>l,CommandGroup:()=>c,CommandInput:()=>a,CommandItem:()=>d,CommandList:()=>i,CommandSeparator:()=>m,CommandShortcut:()=>p});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Command() from the server but Command is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","Command"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandDialog() from the server but CommandDialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandDialog"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandInput() from the server but CommandInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandInput"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandList() from the server but CommandList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandList"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandEmpty() from the server but CommandEmpty is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandEmpty"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandGroup() from the server but CommandGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandGroup"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandItem() from the server but CommandItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandItem"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandShortcut() from the server but CommandShortcut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandShortcut"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call CommandSeparator() from the server but CommandSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\command.tsx","CommandSeparator")},66310:(e,t,o)=>{"use strict";o.r(t),o.d(t,{DropdownMenu:()=>l,DropdownMenuCheckboxItem:()=>f,DropdownMenuContent:()=>p,DropdownMenuGroup:()=>m,DropdownMenuItem:()=>u,DropdownMenuLabel:()=>b,DropdownMenuPortal:()=>c,DropdownMenuRadioGroup:()=>v,DropdownMenuRadioItem:()=>h,DropdownMenuSeparator:()=>g,DropdownMenuShortcut:()=>C,DropdownMenuTrigger:()=>d});var r=o(3641);o(44508);var n=o(59035),s=o(48411),a=o(22631),i=o(10244);function l({...e}){return(0,r.jsx)(n.bL,{"data-slot":"dropdown-menu",...e})}function c({...e}){return(0,r.jsx)(n.ZL,{"data-slot":"dropdown-menu-portal",...e})}function d({...e}){return(0,r.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...e})}function p({className:e,sideOffset:t=4,...o}){return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...o})})}function m({...e}){return(0,r.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...e})}function u({className:e,inset:t,variant:o="default",...s}){return(0,r.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":o,className:(0,i.cn)("focus:bg-accent/5 focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","cursor-pointer",e),...s})}function f({className:e,children:t,checked:o,...a}){return(0,r.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:o,...a,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(s.A,{className:"size-4"})})}),t]})}function v({...e}){return(0,r.jsx)(n.z6,{"data-slot":"dropdown-menu-radio-group",...e})}function h({className:e,children:t,...o}){return(0,r.jsxs)(n.hN,{"data-slot":"dropdown-menu-radio-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(a.A,{className:"size-2 fill-current"})})}),t]})}function b({className:e,inset:t,...o}){return(0,r.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,i.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...o})}function g({className:e,...t}){return(0,r.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,i.cn)("bg-border -mx-1 my-1 h-px",e),...t})}function C({className:e,...t}){return(0,r.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,i.cn)("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}},72024:(e,t,o)=>{"use strict";o.d(t,{HoppingLogo:()=>n});var r=o(87741);(0,r.registerClientReference)(function(){throw Error("Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\layout\\logo.tsx","Logo");let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call HoppingLogo() from the server but HoppingLogo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\layout\\logo.tsx","HoppingLogo")},76021:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Label:()=>a});var r=o(3641);o(44508);var n=o(84908),s=o(10244);function a({className:e,...t}){return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},76370:(e,t,o)=>{"use strict";o.d(t,{HoppingLogo:()=>c});var r=o(3641),n=o(44508),s=o(30427),a=o(54411),i=o(10244);let l=({width:e=50,height:t=32,fill:o="#fff",className:n,...a})=>{let l=(0,s.useRouter)();return(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:t,viewBox:"90 12 100 64",onClick:()=>l.push("/"),className:(0,i.cn)("logo cursor-pointer",n),...a,children:(0,r.jsx)("path",{d:"M171.633,15.8336l-1.7284,6.2499c-.0915.3309-.4369.5221-.7659.4239l-28.9937-8.6507c-.1928-.0575-.4016-.0167-.5586.1092l-28.7143,23.0269c-.0838.0672-.1839.1112-.2901.1276l-17.0849,2.6329c-.3163.0488-.5419.3327-.5178.6519l.0742.9817c.0237.3136.2809.5583.5953.5664l19.8448.513.2263.0063,14.6634-7.8328c.2053-.1097.455-.0936.6445.0415l10.3884,7.4053c.1629.1161.2589.3045.2571.5045l-.0876,9.826c-.0011.1272.0373.2515.11.3559l14.6133,20.9682c.1146.1644.3024.2624.5028.2624h4.626c.4615,0,.7574-.4908.542-.8989l-10.4155-19.7312c-.1019-.193-.0934-.4255.0221-.6106l5.4305-8.6994c.0591-.0947.143-.1715.2425-.222l19.415-9.8522c.1973-.1001.4332-.0861.6172.0366l5.5481,3.6981c.1007.0671.2189.1029.3399.1029h5.0407c.4881,0,.7804-.5429.5116-.9503l-13.9967-21.2171c-.2898-.4393-.962-.3331-1.1022.1741Z",fill:o,strokeWidth:"0"})})},c=e=>{let t=(0,n.useRef)(null),o=(0,r.jsx)(l,{ref:t,...e}),[s,i]=(0,a.A)(o);return(0,n.useEffect)(()=>{let e=t.current,o=null!==e&&e.classList.contains("animate-hop");if(i&&e&&!o)e.classList.add("animate-hop");else if(e&&o){let t=()=>{e.classList.remove("animate-hop"),e.removeEventListener("animationiteration",t)};e.addEventListener("animationiteration",t)}},[i]),s}},76572:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Slider:()=>i});var r=o(3641),n=o(44508),s=o(27537),a=o(10244);function i({className:e,defaultValue:t,value:o,min:i=0,max:l=100,...c}){let d=n.useMemo(()=>Array.isArray(o)?o:Array.isArray(t)?t:[i,l],[o,t,i,l]);return(0,r.jsxs)(s.bL,{"data-slot":"slider",defaultValue:t,value:o,min:i,max:l,className:(0,a.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...c,children:[(0,r.jsx)(s.CC,{"data-slot":"slider-track",className:(0,a.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,r.jsx)(s.Q6,{"data-slot":"slider-range",className:(0,a.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:d.length},(e,t)=>(0,r.jsx)(s.zi,{"data-slot":"slider-thumb",className:"border-primary bg-accent block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50 cursor-pointer"},t))]})}},78617:(e,t,o)=>{"use strict";o.d(t,{ReactQueryProvider:()=>a});var r=o(3641),n=o(73903),s=o(41122);function a({children:e}){let t=new n.E;return(0,r.jsx)(s.Ht,{client:t,children:e})}},81371:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Table:()=>s,TableBody:()=>i,TableCaption:()=>m,TableCell:()=>p,TableFooter:()=>l,TableHead:()=>d,TableHeader:()=>a,TableRow:()=>c});var r=o(3641);o(44508);var n=o(10244);function s({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function a({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tfoot",{"data-slot":"table-footer",className:(0,n.cn)("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0",e),...t})}function c({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-accent/5 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function p({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function m({className:e,...t}){return(0,r.jsx)("caption",{"data-slot":"table-caption",className:(0,n.cn)("text-muted-foreground mt-4 text-sm",e),...t})}},82573:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Select:()=>n,SelectContent:()=>s,SelectGroup:()=>a,SelectItem:()=>i,SelectLabel:()=>l,SelectScrollDownButton:()=>c,SelectScrollUpButton:()=>d,SelectSeparator:()=>p,SelectTrigger:()=>m,SelectValue:()=>u});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Select() from the server but Select is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","Select"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectContent() from the server but SelectContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectContent"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectGroup() from the server but SelectGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectGroup"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectItem() from the server but SelectItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectItem"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectLabel() from the server but SelectLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectLabel"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectScrollDownButton() from the server but SelectScrollDownButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectScrollDownButton"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectScrollUpButton() from the server but SelectScrollUpButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectScrollUpButton"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectSeparator() from the server but SelectSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectSeparator"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectTrigger() from the server but SelectTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectTrigger"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call SelectValue() from the server but SelectValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\select.tsx","SelectValue")},84533:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Dialog:()=>n,DialogClose:()=>s,DialogContent:()=>a,DialogDescription:()=>i,DialogFooter:()=>l,DialogHeader:()=>c,DialogOverlay:()=>d,DialogPortal:()=>p,DialogTitle:()=>m,DialogTrigger:()=>u});var r=o(87741);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call Dialog() from the server but Dialog is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","Dialog"),s=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogClose() from the server but DialogClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogClose"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogContent() from the server but DialogContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogContent"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogDescription() from the server but DialogDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogDescription"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogFooter() from the server but DialogFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogFooter"),c=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogHeader() from the server but DialogHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogHeader"),d=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogOverlay() from the server but DialogOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogOverlay"),p=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogPortal() from the server but DialogPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogPortal"),m=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogTitle() from the server but DialogTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogTitle"),u=(0,r.registerClientReference)(function(){throw Error("Attempted to call DialogTrigger() from the server but DialogTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\dialog.tsx","DialogTrigger")},84774:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Separator:()=>a});var r=o(3641);o(44508);var n=o(21585),s=o(10244);function a({className:e,orientation:t="horizontal",decorative:o=!0,...a}){return(0,r.jsx)(n.b,{"data-slot":"separator-root",decorative:o,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...a})}},85304:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Separator:()=>r});let r=(0,o(87741).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Documents\\Python\\Roo-Code\\apps\\web-evals\\src\\components\\ui\\separator.tsx","Separator")},86384:(e,t,o)=>{Promise.resolve().then(o.bind(o,72024)),Promise.resolve().then(o.bind(o,11987)),Promise.resolve().then(o.bind(o,39012)),Promise.resolve().then(o.bind(o,46390)),Promise.resolve().then(o.bind(o,64582)),Promise.resolve().then(o.bind(o,84533)),Promise.resolve().then(o.bind(o,46886)),Promise.resolve().then(o.bind(o,26160)),Promise.resolve().then(o.bind(o,25655)),Promise.resolve().then(o.bind(o,16435)),Promise.resolve().then(o.bind(o,41582)),Promise.resolve().then(o.bind(o,37194)),Promise.resolve().then(o.bind(o,82573)),Promise.resolve().then(o.bind(o,85304)),Promise.resolve().then(o.bind(o,54730)),Promise.resolve().then(o.bind(o,50266)),Promise.resolve().then(o.bind(o,2809)),Promise.resolve().then(o.bind(o,8817)),Promise.resolve().then(o.bind(o,21212)),Promise.resolve().then(o.bind(o,97664))},88773:()=>{},96112:(e,t,o)=>{Promise.resolve().then(o.bind(o,76370)),Promise.resolve().then(o.bind(o,78617)),Promise.resolve().then(o.bind(o,24478)),Promise.resolve().then(o.bind(o,20164)),Promise.resolve().then(o.bind(o,46016)),Promise.resolve().then(o.bind(o,49431)),Promise.resolve().then(o.bind(o,62604)),Promise.resolve().then(o.bind(o,66310)),Promise.resolve().then(o.bind(o,61805)),Promise.resolve().then(o.bind(o,76021)),Promise.resolve().then(o.bind(o,35940)),Promise.resolve().then(o.bind(o,1196)),Promise.resolve().then(o.bind(o,54927)),Promise.resolve().then(o.bind(o,84774)),Promise.resolve().then(o.bind(o,76572)),Promise.resolve().then(o.bind(o,176)),Promise.resolve().then(o.bind(o,81371)),Promise.resolve().then(o.bind(o,16667)),Promise.resolve().then(o.bind(o,8442)),Promise.resolve().then(o.bind(o,36118))},99115:(e,t,o)=>{"use strict";o.d(t,{$:()=>l,r:()=>i});var r=o(3641);o(44508);var n=o(36118),s=o(28377),a=o(10244);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:opacity-80 active:scale-95 cursor-pointer",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs [&_svg]:text-accent",destructive:"bg-destructive text-white shadow-xs focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input",secondary:"bg-secondary text-secondary-foreground shadow-xs [&_svg]:text-ring",ghost:"hover:bg-primary hover:text-primary-foreground",link:"text-accent underline-offset-4 hover:underline h-4! px-1! rounded-none",input:"bg-input text-input-foreground active:scale-100 shadow-xs"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 px-3 has-[>svg]:px-2.5 text-sm",lg:"h-10 px-6 has-[>svg]:px-4 text-lg",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:o,asChild:s=!1,...l}){let c=s?n.Slot:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,a.cn)(i({variant:t,size:o,className:e})),...l})}}};