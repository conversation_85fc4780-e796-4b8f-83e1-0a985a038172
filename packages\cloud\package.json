{"name": "@roo-code-134/cloud", "description": "Roo Code 134 Cloud VSCode integration.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "test": "vitest run", "clean": "rimraf dist .turbo"}, "dependencies": {"@roo-code-134/telemetry": "workspace:^", "@roo-code-134/types": "workspace:^", "axios": "^1.7.4", "zod": "^3.24.2"}, "devDependencies": {"@roo-code-134/config-eslint": "workspace:^", "@roo-code-134/config-typescript": "workspace:^", "@types/node": "^22.15.20", "@types/vscode": "^1.84.0", "vitest": "^3.1.3"}}