import { Anthropic } from "@anthropic-ai/sdk"
import type { ModelInfo } from "@roo-code/types"
import type { ApiHandlerCreateMessageMetadata } from "../api/index"

// Conditional import to handle cases where Langfuse might not be available
let Langfuse: any = null
try {
	Langfuse = require("langfuse").Langfuse
} catch (error) {
	console.warn("[LangfuseTracker] Langfuse package not available:", error)
}

export interface LangfuseConfig {
	enabled: boolean
	publicKey?: string
	secretKey?: string
	baseUrl?: string
	environment?: string
	userId?: string
	sessionId?: string
}

export interface LangfuseTraceData {
	traceId: string
	spanId: string
	systemPrompt: string
	messages: Anthropic.Messages.MessageParam[]
	metadata: ApiHandlerCreateMessageMetadata
	modelInfo: ModelInfo
	modelId: string
	provider: string
	startTime: number
}

export interface LangfuseCompletionData {
	response: string
	reasoning?: string
	inputTokens: number
	outputTokens: number
	cacheWriteTokens?: number
	cacheReadTokens?: number
	totalCost?: number
	endTime: number
	error?: string
}

/**
 * Langfuse tracking adapter that provides a clean interface for LLM observability.
 * Uses the Adapter pattern to wrap Langfuse SDK functionality.
 */
export class LangfuseTracker {
	private langfuse: any = null
	private config: LangfuseConfig
	private activeTraces = new Map<string, any>()
	private activeSpans = new Map<string, any>()

	constructor(config: LangfuseConfig) {
		this.config = config
		this.initialize()
	}

	private initialize(): void {
		if (!this.config.enabled || !this.config.publicKey || !this.config.secretKey || !Langfuse) {
			if (this.config.enabled && !Langfuse) {
				console.warn("[LangfuseTracker] Langfuse tracking enabled but package not available")
			}
			return
		}

		try {
			this.langfuse = new Langfuse({
				publicKey: this.config.publicKey,
				secretKey: this.config.secretKey,
				baseUrl: this.config.baseUrl,
				environment: this.config.environment,
			})
			console.log("[LangfuseTracker] Langfuse initialized successfully")
		} catch (error) {
			console.error("[LangfuseTracker] Failed to initialize Langfuse:", error)
			this.langfuse = null
		}
	}

	/**
	 * Start tracking an LLM request
	 */
	public startTrace(data: LangfuseTraceData): string | null {
		if (!this.langfuse) return null

		try {
			const trace = this.langfuse.trace({
				id: data.traceId,
				name: `roo-code-134-${data.provider}-request`,
				userId: this.config.userId,
				sessionId: this.config.sessionId || data.metadata.taskId,
				metadata: {
					taskId: data.metadata.taskId,
					mode: data.metadata.mode,
					provider: data.provider,
					modelId: data.modelId,
					modelInfo: data.modelInfo,
					timestamp: data.startTime,
				},
				tags: [data.provider, data.modelId, "roo-code-134"],
			})

			const span = trace.span({
				id: data.spanId,
				name: "llm-completion",
				startTime: new Date(data.startTime),
				input: {
					systemPrompt: data.systemPrompt,
					messages: this.sanitizeMessages(data.messages),
				},
				metadata: {
					provider: data.provider,
					model: data.modelId,
					contextWindow: data.modelInfo.contextWindow,
					maxTokens: data.modelInfo.maxTokens,
					supportsComputerUse: data.modelInfo.supportsComputerUse,
				},
			})

			this.activeTraces.set(data.traceId, trace)
			this.activeSpans.set(data.spanId, span)

			return data.traceId
		} catch (error) {
			console.error("[LangfuseTracker] Failed to start trace:", error)
			return null
		}
	}

	/**
	 * Complete tracking an LLM request
	 */
	public completeTrace(traceId: string, spanId: string, data: LangfuseCompletionData): void {
		if (!this.langfuse) return

		try {
			const span = this.activeSpans.get(spanId)
			if (!span) {
				console.warn(`[LangfuseTracker] Span ${spanId} not found`)
				return
			}

			const endTime = new Date(data.endTime)
			const level = data.error ? "ERROR" : "DEFAULT"

			span.update({
				endTime,
				level,
				output: data.error ? { error: data.error } : { 
					response: data.response,
					reasoning: data.reasoning 
				},
				usage: {
					input: data.inputTokens,
					output: data.outputTokens,
					total: data.inputTokens + data.outputTokens,
					unit: "TOKENS",
					inputCost: this.calculateInputCost(data),
					outputCost: this.calculateOutputCost(data),
					totalCost: data.totalCost,
				},
				metadata: {
					cacheWriteTokens: data.cacheWriteTokens,
					cacheReadTokens: data.cacheReadTokens,
					duration: data.endTime - this.getTraceStartTime(traceId),
				},
			})

			span.end()
			this.activeSpans.delete(spanId)
			this.activeTraces.delete(traceId)
		} catch (error) {
			console.error("[LangfuseTracker] Failed to complete trace:", error)
		}
	}

	/**
	 * Track an error in the LLM request
	 */
	public trackError(traceId: string, spanId: string, error: Error): void {
		if (!this.langfuse) return

		try {
			const span = this.activeSpans.get(spanId)
			if (span) {
				span.update({
					level: "ERROR",
					statusMessage: error.message,
					output: { error: error.message, stack: error.stack },
				})
			}
		} catch (err) {
			console.error("[LangfuseTracker] Failed to track error:", err)
		}
	}

	/**
	 * Update configuration and reinitialize if needed
	 */
	public updateConfig(newConfig: Partial<LangfuseConfig>): void {
		const wasEnabled = this.config.enabled
		this.config = { ...this.config, ...newConfig }
		
		if (!wasEnabled && this.config.enabled) {
			this.initialize()
		} else if (wasEnabled && !this.config.enabled) {
			this.shutdown()
		}
	}

	/**
	 * Flush pending events and shutdown
	 */
	public async shutdown(): Promise<void> {
		if (this.langfuse) {
			try {
				await this.langfuse.shutdownAsync()
			} catch (error) {
				console.error("[LangfuseTracker] Error during shutdown:", error)
			}
			this.langfuse = null
		}
		this.activeTraces.clear()
		this.activeSpans.clear()
	}

	/**
	 * Check if tracking is enabled and configured
	 */
	public get isEnabled(): boolean {
		return this.config.enabled && this.langfuse !== null
	}

	// Private helper methods
	private sanitizeMessages(messages: Anthropic.Messages.MessageParam[]): any[] {
		return messages.map(msg => ({
			role: msg.role,
			content: Array.isArray(msg.content) 
				? msg.content.map(block => {
					if (block.type === 'text') return { type: 'text', text: block.text }
					if (block.type === 'image') return { type: 'image', source: '[IMAGE_DATA_REDACTED]' }
					return block
				})
				: msg.content
		}))
	}

	private calculateInputCost(data: LangfuseCompletionData): number | undefined {
		// This would need to be implemented based on your cost calculation logic
		return undefined
	}

	private calculateOutputCost(data: LangfuseCompletionData): number | undefined {
		// This would need to be implemented based on your cost calculation logic
		return undefined
	}

	private getTraceStartTime(traceId: string): number {
		// You might want to store start times separately for duration calculation
		return Date.now()
	}
}

// Singleton instance for global access
let globalLangfuseTracker: LangfuseTracker | null = null

export function initializeLangfuseTracker(config: LangfuseConfig): LangfuseTracker {
	if (globalLangfuseTracker) {
		globalLangfuseTracker.updateConfig(config)
	} else {
		globalLangfuseTracker = new LangfuseTracker(config)
	}
	return globalLangfuseTracker
}

export function getLangfuseTracker(): LangfuseTracker | null {
	return globalLangfuseTracker
}
