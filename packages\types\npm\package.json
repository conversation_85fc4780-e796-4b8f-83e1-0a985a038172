{"name": "@roo-code-134/types", "version": "1.25.0", "description": "TypeScript type definitions for Roo Code 134.", "publishConfig": {"access": "public", "name": "@roo-code-134/types"}, "author": "Roo Code 134 Team", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/RooCodeInc/Roo-Code-134.git"}, "bugs": {"url": "https://github.com/RooCodeInc/Roo-Code-134/issues"}, "homepage": "https://github.com/RooCodeInc/Roo-Code-134/tree/main/packages/types", "keywords": ["roo", "roo-code-134", "ai"], "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist"]}