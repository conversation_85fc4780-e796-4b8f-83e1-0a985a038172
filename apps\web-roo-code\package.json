{"name": "@roo-code-134/web-roo-code", "version": "0.0.0", "type": "module", "scripts": {"lint": "next lint", "check-types": "tsc --noEmit", "dev": "next dev", "build": "next build", "start": "next start", "drizzle-kit": "dotenvx run -f .env -- tsx node_modules/drizzle-kit/bin.cjs", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:push": "pnpm drizzle-kit push", "db:pull": "pnpm drizzle-kit pull", "db:check": "pnpm drizzle-kit check", "db:up": "pnpm drizzle-kit up", "db:studio": "pnpm drizzle-kit studio"}, "dependencies": {"@libsql/client": "^0.15.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@roo-code/types": "workspace:^", "@tanstack/react-query": "^5.79.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.7.1", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "lucide-react": "^0.513.0", "next": "^15.2.5", "next-themes": "^0.4.6", "posthog-js": "^1.248.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.41"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.54", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.0", "postcss": "^8.5.4", "tailwindcss": "^3.4.17"}}