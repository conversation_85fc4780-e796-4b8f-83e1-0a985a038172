"use strict";exports.id=246,exports.ids=[246],exports.modules={15361:(e,r,t)=>{t.d(r,{Lt:()=>a.<PERSON><PERSON>,Rx:()=>a.<PERSON><PERSON>,Zr:()=>a.<PERSON>,EO:()=>a.AlertDialog<PERSON>ontent,$v:()=>a.AlertDialogDescription,ck:()=>a.<PERSON>ogFooter,wd:()=>a.<PERSON><PERSON>ogHeader,r7:()=>a.AlertDialogTitle,$n:()=>u.$,uB:()=>g.Command,xL:()=>g.CommandEmpty,L$:()=>g.CommandGroup,G7:()=>g.CommandInput,h_:()=>g.CommandItem,oI:()=>g.CommandList,lG:()=>m.<PERSON>alog,Cf:()=>m.DialogContent,Es:()=>m.<PERSON>oot<PERSON>,L3:()=>m.<PERSON>,rI:()=>v.DropdownMenu,SQ:()=>v.DropdownMenuContent,_2:()=>v.DropdownMenuItem,ty:()=>v.DropdownMenuTrigger,MJ:()=>p.FormControl,zB:()=>p.FormField,eI:()=>p.FormItem,lR:()=>p.FormLabel,C5:()=>p.FormMessage,KF:()=>j,AM:()=>w.Popover,hl:()=>w.PopoverContent,Wv:()=>w.PopoverTrigger,FK:()=>N.ScrollArea,Ap:()=>A.Slider,XI:()=>I.Table,BF:()=>I.TableBody,nA:()=>I.TableCell,nd:()=>I.TableHead,A0:()=>I.TableHeader,Hj:()=>I.TableRow,tU:()=>C.Tabs,j7:()=>C.TabsList,Xi:()=>C.TabsTrigger,TM:()=>M});var a=t(20164),s=t(3641),n=t(44508),i=t(36118),o=t(28377),l=t(10244);let d=(0,o.F)("inline-flex items-center justify-center rounded-sm border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:r,asChild:t=!1,...a}){let n=t?i.Slot:"span";return(0,s.jsx)(n,{"data-slot":"badge",className:(0,l.cn)(d({variant:r}),e),...a})}var u=t(99115),g=t(46016),m=t(49431);t(62604);var v=t(66310),p=t(61805);t(76021);var f=t(38131),x=t.n(f),h=t(52819),y=t(60418),b=t(48411),w=t(35940);let k=(0,o.F)("px-2 py-1",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"bg-background"}},defaultVariants:{variant:"default"}}),j=n.forwardRef(({options:e,onValueChange:r,variant:t,defaultValue:a=[],placeholder:i="Select options",maxCount:o=3,modalPopover:d=!1,className:u,...m},v)=>{let[p,f]=n.useState(a),[j,N]=n.useState(!1),A=e=>{let t=p.includes(e)?p.filter(r=>r!==e):[...p,e];f(t),r(t)},I=()=>{let e=p.slice(0,o);f(e),r(e)},C=n.useRef(new Map),M=n.useRef(""),_=n.useCallback((r,t)=>{if(M.current!==t)for(let{obj:{value:r},score:a}of(M.current=t,C.current.clear(),x().go(t,e,{key:"label"})))C.current.set(r,a);return"all"===r?.01*(C.current.size>1):C.current.get(r)??0},[e]);return(0,s.jsxs)(w.Popover,{open:j,onOpenChange:N,modal:d,children:[(0,s.jsx)(w.PopoverTrigger,{asChild:!0,children:(0,s.jsx)("div",{ref:v,...m,onClick:()=>{N(e=>!e)},className:(0,l.cn)("flex w-full rounded-sm min-h-9 h-auto items-center justify-between [&_svg]:pointer-events-auto","font-medium border border-input bg-input hover:opacity-80 cursor-pointer",u),children:p.length>0?(0,s.jsx)("div",{className:"flex justify-between items-center w-full",children:(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-1 p-1",children:[p.slice(0,o).map(r=>(0,s.jsx)(c,{className:(0,l.cn)(k({variant:t})),children:(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)("div",{children:e.find(e=>e.value===r)?.label}),(0,s.jsx)("div",{onClick:e=>{e.stopPropagation(),A(r)},className:"cursor-pointer",children:(0,s.jsx)(h.A,{className:"size-4 rounded-full p-0.5 bg-accent/5"})})]})},r)),p.length>o&&(0,s.jsx)(c,{className:(0,l.cn)("text-ring",k({variant:t})),children:(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)("div",{children:`+ ${p.length-o} more`}),(0,s.jsx)("div",{onClick:e=>{e.stopPropagation(),I()},className:"cursor-pointer",children:(0,s.jsx)(h.A,{className:"size-4 rounded-full p-0.5 bg-ring/5"})})]})})]})}):(0,s.jsxs)("div",{className:"flex items-center justify-between w-full mx-auto",children:[(0,s.jsx)("span",{className:"text-muted-foreground mx-3",children:i}),(0,s.jsx)(y.A,{className:"opacity-50 size-4 mx-2"})]})})}),(0,s.jsx)(w.PopoverContent,{className:"p-0 w-[var(--radix-popover-trigger-width)]",align:"start",onEscapeKeyDown:()=>N(!1),children:(0,s.jsxs)(g.Command,{filter:_,children:[(0,s.jsx)(g.CommandInput,{placeholder:"Search",onKeyDown:e=>{if("Enter"===e.key)N(!0);else if("Backspace"===e.key&&!e.currentTarget.value){let e=[...p];e.pop(),f(e),r(e)}}}),(0,s.jsxs)(g.CommandList,{children:[(0,s.jsx)(g.CommandEmpty,{children:"No results found."}),(0,s.jsxs)(g.CommandGroup,{children:[e.map(e=>(0,s.jsxs)(g.CommandItem,{value:e.value,onSelect:()=>A(e.value),className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:e.label}),(0,s.jsx)(b.A,{className:(0,l.cn)("text-accent group-data-[selected=true]:text-accent-foreground size-4",{"opacity-0":!p.includes(e.value)})})]},e.value)),(0,s.jsx)(g.CommandItem,{value:"all",onSelect:()=>{let e=Array.from(C.current.keys());if(p.length===e.length&&p.sort().join(",")===e.sort().join(",")){f([]),r([]);return}f(e),r(e)},className:"flex items-center justify-between",children:(0,s.jsx)("span",{children:"Select All"})},"all")]})]})]})})]})});j.displayName="MultiSelect";var N=t(1196);t(54927),t(84774);var A=t(76572);t(176);var I=t(81371),C=t(16667);function M({className:e,...r}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,l.cn)("placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-sm px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50","border border-input bg-input",e),...r})}t(8442)},29230:(e,r,t)=>{t.r(r),t.d(r,{default:()=>s});var a=t(11280);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},57177:(e,r,t)=>{t.d(r,{ut:()=>D,UT:()=>M,xF:()=>E,w8:()=>R,x1:()=>_});var a={};t.r(a),t.d(a,{runs:()=>m,runsRelations:()=>v,schema:()=>b,taskMetrics:()=>x,tasks:()=>p,tasksRelations:()=>f,toolErrors:()=>h,toolErrorsRelations:()=>y});var s=t(59593),n=t(59166),i=t(69289),o=t(95771),l=t(85192),d=t(92132),c=t(77036),u=t(37860),g=t(3286);let m=(0,s.cJ)("runs",{id:(0,n.nd)().primaryKey().generatedAlwaysAsIdentity(),taskMetricsId:(0,n.nd)("task_metrics_id").references(()=>x.id),model:(0,i.Qq)().notNull(),description:(0,i.Qq)(),settings:(0,o.Fx)().$type(),pid:(0,n.nd)(),socketPath:(0,i.Qq)("socket_path").notNull(),concurrency:(0,n.nd)().default(2).notNull(),passed:(0,n.nd)().default(0).notNull(),failed:(0,n.nd)().default(0).notNull(),createdAt:(0,l.vE)("created_at").notNull()}),v=(0,g.K1)(m,({one:e})=>({taskMetrics:e(x,{fields:[m.taskMetricsId],references:[x.id]})})),p=(0,s.cJ)("tasks",{id:(0,n.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,n.nd)("run_id").references(()=>m.id).notNull(),taskMetricsId:(0,n.nd)("task_metrics_id").references(()=>x.id),language:(0,i.Qq)().notNull().$type(),exercise:(0,i.Qq)().notNull(),passed:(0,d.zM)(),startedAt:(0,l.vE)("started_at"),finishedAt:(0,l.vE)("finished_at"),createdAt:(0,l.vE)("created_at").notNull()},e=>[(0,c.GL)("tasks_language_exercise_idx").on(e.runId,e.language,e.exercise)]),f=(0,g.K1)(p,({one:e})=>({run:e(m,{fields:[p.runId],references:[m.id]}),taskMetrics:e(x,{fields:[p.taskMetricsId],references:[x.id]})})),x=(0,s.cJ)("taskMetrics",{id:(0,n.nd)().primaryKey().generatedAlwaysAsIdentity(),tokensIn:(0,n.nd)("tokens_in").notNull(),tokensOut:(0,n.nd)("tokens_out").notNull(),tokensContext:(0,n.nd)("tokens_context").notNull(),cacheWrites:(0,n.nd)("cache_writes").notNull(),cacheReads:(0,n.nd)("cache_reads").notNull(),cost:(0,u.x)().notNull(),duration:(0,n.nd)().notNull(),toolUsage:(0,o.Fx)("tool_usage").$type(),createdAt:(0,l.vE)("created_at").notNull()}),h=(0,s.cJ)("toolErrors",{id:(0,n.nd)().primaryKey().generatedAlwaysAsIdentity(),runId:(0,n.nd)("run_id").references(()=>m.id),taskId:(0,n.nd)("task_id").references(()=>p.id),toolName:(0,i.Qq)("tool_name").notNull().$type(),error:(0,i.Qq)().notNull(),createdAt:(0,l.vE)("created_at").notNull()}),y=(0,g.K1)(h,({one:e})=>({run:e(m,{fields:[h.runId],references:[m.id]}),task:e(p,{fields:[h.taskId],references:[p.id]})})),b={runs:m,runsRelations:v,tasks:p,tasksRelations:f,taskMetrics:x,toolErrors:h,toolErrorsRelations:y};var w=t(7361);class k extends Error{}class j extends Error{}var N=t(71787);let A=(0,t(61786).A)(process.env.DATABASE_URL,{prepare:!1}),I=(0,N.f)({client:A,schema:a});var C=t(3131);let M=async e=>{let r=(await I.insert(p).values({...e,createdAt:new Date}).returning())[0];if(!r)throw new j;return r},_=async e=>I.query.tasks.findMany({where:(0,w.eq)(p.runId,e),with:{taskMetrics:!0},orderBy:(0,C.Y)(p.id)}),D=async e=>{let r=(await I.insert(b.runs).values({...e,createdAt:new Date}).returning())[0];if(!r)throw new j;return r},E=async e=>{let r=await I.query.runs.findFirst({where:(0,w.eq)(b.runs.id,e),columns:{taskMetricsId:!0}});if(!r)throw new k;let t=await I.query.tasks.findMany({where:(0,w.eq)(b.tasks.runId,e),columns:{id:!0,taskMetricsId:!0}});await I.delete(b.toolErrors).where((0,w.RV)(b.toolErrors.taskId,t.map(({id:e})=>e))),await I.delete(b.tasks).where((0,w.eq)(b.tasks.runId,e)),await I.delete(b.toolErrors).where((0,w.eq)(b.toolErrors.runId,e)),await I.delete(b.runs).where((0,w.eq)(b.runs.id,e));let a=t.map(({taskMetricsId:e})=>e).filter(e=>null!=e);a.push(r.taskMetricsId??-1),await I.delete(b.taskMetrics).where((0,w.RV)(b.taskMetrics.id,a))};var T=t(33873);t(79748);var F=t(79551);let q=T.dirname((0,F.fileURLToPath)("file:///D:/Documents/Python/Roo-Code/packages/evals/src/exercises/index.ts"));T.resolve(q,"..","..","..","..","..","evals");let R=["go","java","javascript","python","rust"]}};